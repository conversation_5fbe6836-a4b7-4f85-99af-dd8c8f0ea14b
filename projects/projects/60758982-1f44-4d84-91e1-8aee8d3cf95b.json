{"project_id": "60758982-1f44-4d84-91e1-8aee8d3cf95b", "name": "yFlow", "description": "Proyecto de pruebas automatizadas para la plataforma yFlow", "tags": ["automation", "qa", "yflow", "smoke-tests"], "test_suites": {"825cf0fc-5215-4b11-bdfc-7c2b08ba55ee": {"suite_id": "825cf0fc-5215-4b11-bdfc-7c2b08ba55ee", "name": "SmokeTest", "description": "Suite de pruebas de humo para verificar funcionalidades básicas de yFlow", "tags": ["smoke", "critical", "regression"], "test_cases": {"162d3b40-f5ee-46ff-81ab-d1e8077cdf0c": {"test_id": "162d3b40-f5ee-46ff-81ab-d1e8077cdf0c", "name": "SearchFlow", "description": "Prueba de búsqueda de flujos en la plataforma yFlow", "instrucciones": "Ve al sitio web https://yflow-yoizen-qa.ysocial.net.\nIngresa el usuario rgroisman y la contraseña Gurruchaga487 y haz clic en el botón para iniciar sesión.\nUna vez dentro, busca un campo de búsqueda en la parte superior de la página.\nEscribe \"smoketestv1\" en el campo de búsqueda y presiona Enter o haz clic en el botón de búsqueda.\nVerifica que aparezca el flujo \"smoketestv1\" en los resultados de búsqueda.\nSi encuentras el flujo, escribe \"Búsqueda exitosa\".\nSi no encuentras el flujo o hay algún problema, describe exactamente qué pasó.", "historia_de_usuario": "", "gherkin": "Feature: Búsqueda de flujos\n  Como usuario de yFlow\n  Quiero poder buscar flujos por nombre\n  Para encontrar rápidamente el contenido que necesito\n\n  Scenario: Búsqueda exitosa de flujo existente\n    Given que estoy en la página de login de yFlow\n    When ingreso las credenciales válidas (rgroisman/Gurruchaga487)\n    And hago clic en el botón de iniciar sesión\n    And busco \"smoketestv1\" en el campo de búsqueda\n    Then debería ver el flujo \"smoketestv1\" en los resultados\n    And debería poder confirmar \"Búsqueda exitosa\" ", "url": "https://yflow-yoizen-qa.ysocial.net", "tags": ["search", "smoke", "critical"], "created_at": "2025-05-28T01:48:04.395096", "updated_at": "2025-06-04T00:03:17.793116", "history_files": ["tests/smoke_test_20250604000317/history.json"], "status": "Passed", "last_execution": "2025-06-04T00:03:17.792946", "code": "", "framework": ""}, "416579b4-c853-4eb7-8ca1-9b806d300257": {"test_id": "416579b4-c853-4eb7-8ca1-9b806d300257", "name": "<PERSON><PERSON>", "description": "Prueba de inicio de sesión en la plataforma yFlow", "instrucciones": "Ve al sitio web https://yflow-yoizen-qa.ysocial.net.\nIngresa el usuario rgroisman y la contraseña Gurruchaga487 y haz clic en el botón para iniciar sesión.\nUna vez dentro, deberías ver una pantalla con una lista de flujos o bots.\nVerifica que puedas ver el panel principal con la lista de flujos.\nSi todo funciona correctamente, escribe \"Login exitoso\".\nSi encuentras algún problema, describe exactamente qué pasó.", "historia_de_usuario": "", "gherkin": "Feature: Inicio de sesión\n  Como usuario registrado de yFlow\n  Quiero poder iniciar sesión con mis credenciales\n  Para acceder a mi panel de control\n\n  Scenario: Login exitoso con credenciales válidas\n    Given que estoy en la página de login de yFlow\n    When ingreso el usuario \"rgroisman\"\n    And ingreso la contraseña \"Gurruchaga487\"\n    And hago clic en el botón de iniciar sesión\n    Then debería ver el panel principal con la lista de flujos\n    And deber<PERSON> poder confirmar \"Login exitoso\" ", "url": "https://yflow-yoizen-qa.ysocial.net", "tags": ["login", "authentication", "smoke", "critical"], "created_at": "2025-05-28T01:48:04.396098", "updated_at": "2025-06-04T01:04:44.220647", "history_files": ["tests\\smoke_test_20250528150524\\history.json", "tests/smoke_test_20250604004126/history.json", "tests/smoke_test_20250604005220/history.json", "tests/smoke_test_20250604010436/history.json"], "status": "Failed", "last_execution": "2025-06-04T01:04:44.220645", "code": "", "framework": ""}, "61109999-1548-4131-be6e-f51f041b942b": {"test_id": "61109999-1548-4131-be6e-f51f041b942b", "name": "CreateFlow", "description": "Prueba de creación de un nuevo flujo/bot en la plataforma yFlow", "instrucciones": "Ve al sitio web https://yflow-yoizen-qa.ysocial.net.\nIngresa el usuario rgroisman y la contraseña Gurruchaga487 y haz clic en el botón para iniciar sesión.\nUna vez dentro, busca el botón para crear un nuevo bot (puede ser un botón con un signo '+' o que diga 'Nuevo bot').\nHaz clic en ese botón.\nEn la ventana que aparece, escribe 'smoketestQAAgents' como nombre del bot.\nLuego selecciona 'Whatsapp' en la lista desplegable de canales.\nIMPORTANTE: Busca el botón que dice 'Crear' o 'Guardar' (NO el que dice 'Cancelar') y haz clic en él.\nEspera a que se complete la creación y verifica que el nuevo bot 'smoketestQAAgents' aparezca en la lista.\nSi todo funciona correctamente, escribe 'Creación de flujo exitosa'.\nSi encuentras algún problema, describe exactamente qué pasó.", "historia_de_usuario": "", "gherkin": "Feature: Creación de flujos\n  Como usuario de yFlow\n  Quiero poder crear nuevos bots/flujos\n  Para automatizar conversaciones en diferentes canales\n\n  Scenario: Creación exitosa de un nuevo bot para WhatsApp\n    Given que estoy logueado en yFlow\n    When hago clic en el botón para crear un nuevo bot\n    And ingreso \"smoketestQAAgents\" como nombre del bot\n    And selecciono \"Whatsapp\" como canal\n    And hago clic en el botón \"Crear\"\n    Then debería ver el nuevo bot \"smoketestQAAgents\" en la lista\n    And debería poder confirmar \"Creación de flujo exitosa\" ", "url": "https://yflow-yoizen-qa.ysocial.net", "tags": ["create", "bot", "whatsapp", "smoke", "critical"], "created_at": "2025-05-28T01:48:04.396098", "updated_at": "2025-05-28T12:13:51.343265", "history_files": [], "status": "Failed", "last_execution": "2025-05-28T12:13:51.342263", "code": "", "framework": ""}}, "created_at": "2025-05-28T01:48:04.394101", "updated_at": "2025-05-28T01:48:04.396098"}}, "created_at": "2025-05-28T01:48:04.394101", "updated_at": "2025-05-28T01:48:04.394101"}