"""API web para exponer servicios de pruebas y automatizaciu00f3n."""

import os
import sys
import asyncio
import concurrent.futures
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from dotenv import load_dotenv

from fastapi import FastAPI, HTTPException, Depends, Body, File, UploadFile, Form, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field

# Importar las nuevas rutas modulares
from src.API.project_routes import router as project_router
from src.API.suite_routes import router as suite_router
from src.API.testcase_routes import router as testcase_router
from src.API.prompt_routes import router as prompt_router

# Importar modelos desde el archivo separado
from src.API.models import (
    SmokeTestRequest,
    FullTestRequest,
    GherkinRequest,
    CodeGenerationRequest,
    EnhanceStoryRequest,
    GenerateManualTestsRequest,
    GenerateGherkinRequest,
    SaveHistoryRequest,
    SummarizeRequest
)

# Cargar variables de entorno
load_dotenv()

# Importar el servicio central
from src.Core.test_service import TestService
# Importar el nuevo servicio de prompts
from src.Core.prompt_service import PromptService

# Verificar que la API key estu00e9 configurada
if not os.environ.get("GOOGLE_API_KEY"):
    raise ValueError("No se encontru00f3 la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")

# Crear la aplicaciu00f3n FastAPI
app = FastAPI(
    title="QA Agent API",
    description="API para automatizaciu00f3n de pruebas, generaciu00f3n de casos de prueba y gestiou00f3n de proyectos.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS para permitir solicitudes desde cualquier origen
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Permitir todos los oru00edgenes en desarrollo
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Incluir las rutas modulares
app.include_router(project_router)
app.include_router(suite_router)
app.include_router(testcase_router)
app.include_router(prompt_router)

# Montar archivos estáticos para capturas de pantalla
# Crear directorio de tests si no existe
tests_dir = "tests"
if not os.path.exists(tests_dir):
    os.makedirs(tests_dir, exist_ok=True)

# Montar el directorio de tests para servir capturas de pantalla
app.mount("/api/screenshots", StaticFiles(directory=tests_dir), name="screenshots")

# Funciou00f3n para obtener una instancia del servicio de pruebas
def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

# Función para obtener una instancia del servicio de prompts
def get_prompt_service():
    """Crea y devuelve una instancia del servicio de prompts."""
    return PromptService()

def transform_backend_response_to_frontend_format(backend_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Transforma la respuesta del backend al formato esperado por el frontend.

    Args:
        backend_result: Resultado del backend

    Returns:
        Datos en el formato esperado por el frontend
    """
    # Extraer datos del historial si existe
    history = backend_result.get("history", {})

    # Transformar acciones
    actions = []
    if "model_actions" in history:
        for i, action_data in enumerate(history["model_actions"]):
            action_type = "unknown"
            details = action_data

            # Determinar el tipo de acción
            if isinstance(action_data, dict):
                for key in action_data.keys():
                    if key in ["click_element", "input_text", "navigate", "get_xpath_of_element"]:
                        action_type = key
                        break

            actions.append({
                "step": i + 1,
                "type": action_type,
                "details": details
            })

    # Transformar resultados
    results = []
    if "extracted_content" in history:
        for i, content in enumerate(history["extracted_content"]):
            results.append({
                "step": i + 1,
                "content": str(content),
                "success": True  # Asumir éxito si no hay información específica
            })

    # Transformar elementos
    elements = []
    if "model_actions" in history:
        for i, action_data in enumerate(history["model_actions"]):
            if isinstance(action_data, dict) and "interacted_element" in action_data:
                element_info = action_data["interacted_element"]
                if element_info:
                    # Extraer información del elemento
                    element_str = str(element_info)
                    import re
                    xpath_match = re.search(r"xpath=['\"]([^'\"]+)['\"]", element_str)
                    tag_match = re.search(r"tag=['\"]([^'\"]+)['\"]", element_str)

                    elements.append({
                        "step": i + 1,
                        "tag_name": tag_match.group(1) if tag_match else "unknown",
                        "xpath": xpath_match.group(1) if xpath_match else "",
                        "attributes": {}  # Podríamos extraer más atributos si están disponibles
                    })

    # Transformar URLs
    urls = []
    if "urls" in history:
        for i, url in enumerate(history["urls"]):
            urls.append({
                "step": i + 1,
                "url": str(url),
                "title": ""  # No tenemos título en el historial actual
            })

    # Transformar errores
    errors = history.get("errors", [])

    # Transformar capturas de pantalla
    screenshots = []
    screenshot_paths = backend_result.get("screenshot_paths", [])
    for path in screenshot_paths:
        # Convertir rutas de archivos a URLs servibles
        if isinstance(path, str):
            if path.startswith("data:image"):
                # Es una imagen base64, mantener como está
                screenshots.append(path)
            elif path.startswith("http"):
                # Es una URL, mantener como está
                screenshots.append(path)
            elif path.startswith("tests/"):
                # Es una ruta relativa desde el directorio tests
                screenshots.append(f"/api/screenshots/{path[6:]}")  # Remover "tests/" del inicio
            elif "/" in path:
                # Es una ruta que contiene directorios
                # Extraer solo la parte después de "tests" si existe
                if "tests/" in path:
                    relative_path = path.split("tests/", 1)[1]
                    screenshots.append(f"/api/screenshots/{relative_path}")
                else:
                    screenshots.append(f"/api/screenshots/{path}")
            else:
                # Es solo un nombre de archivo
                screenshots.append(f"/api/screenshots/{path}")
        else:
            # Convertir a string si no es string
            screenshots.append(str(path))

    # Crear metadata
    metadata = {
        "start_time": None,  # No disponible en el formato actual
        "end_time": None,    # No disponible en el formato actual
        "total_steps": len(actions),
        "success": backend_result.get("success", False)
    }

    # Crear la respuesta en el formato esperado por el frontend
    frontend_response = {
        "actions": actions,
        "results": results,
        "elements": elements,
        "urls": urls,
        "errors": errors,
        "screenshots": screenshots,
        "metadata": metadata,
        "generated_gherkin": backend_result.get("gherkin_scenario", ""),
        "generatedGherkin": backend_result.get("gherkin_scenario", ""),  # Ambos formatos por compatibilidad
        "test_id": backend_result.get("test_id"),
        "history_path": backend_result.get("history_path")
    }

    return frontend_response

def clean_data_for_json_serialization(data: Any) -> Any:
    """
    Limpia los datos para que sean serializables en JSON.
    Convierte objetos no serializables a strings o diccionarios.

    Args:
        data: Datos a limpiar

    Returns:
        Datos limpios serializables en JSON
    """
    if data is None:
        return None
    elif isinstance(data, (str, int, float, bool)):
        return data
    elif isinstance(data, dict):
        return {key: clean_data_for_json_serialization(value) for key, value in data.items()}
    elif isinstance(data, (list, tuple)):
        return [clean_data_for_json_serialization(item) for item in data]
    elif hasattr(data, '__class__') and 'DOMHistoryElement' in str(data.__class__):
        # Manejar específicamente objetos DOMHistoryElement
        try:
            # Intentar extraer información útil del objeto
            element_str = str(data)
            # Buscar xpath en la representación string
            import re
            xpath_match = re.search(r"xpath=['\"]([^'\"]+)['\"]", element_str)
            tag_match = re.search(r"tag=['\"]([^'\"]+)['\"]", element_str)

            result = {
                "type": "DOMHistoryElement",
                "string_representation": element_str
            }

            if xpath_match:
                result["xpath"] = xpath_match.group(1)
            if tag_match:
                result["tag"] = tag_match.group(1)

            return result
        except:
            return {"type": "DOMHistoryElement", "string_representation": str(data)}
    elif hasattr(data, '__dict__'):
        # Para objetos con atributos, convertir a diccionario
        try:
            return {key: clean_data_for_json_serialization(value) for key, value in data.__dict__.items()}
        except:
            return str(data)
    else:
        # Para cualquier otro tipo, convertir a string
        return str(data)

# Función helper para ejecutar tests en un proceso separado
def run_smoke_test_sync(instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un smoke test de manera síncrona en un proceso separado."""
    try:
        # Crear el servicio de pruebas
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Usar el método sincrónico que maneja su propio bucle de eventos
        result = test_service.run_smoke_test(
            instructions=instructions,
            url=url,
            user_story=user_story
        )

        # Asegurar que siempre devolvemos un resultado válido
        if result is None:
            return {
                "success": False,
                "error": "El test no devolvió ningún resultado"
            }
        
        # Si el resultado ya indica éxito/fallo, devolverlo tal como está
        if isinstance(result, dict) and 'success' in result:
            return result
        
        # Si el resultado no tiene el formato esperado, envolverlo
        return {
            "success": True,
            "result": result
        }

    except Exception as e:
        error_msg = str(e)
        print(f"Error en run_smoke_test_sync: {error_msg}")
        
        # No re-lanzar la excepción, solo devolver el error
        return {
            "success": False,
            "error": error_msg,
            "test_id": None,
            "history": None,
            "screenshot_paths": [],
            "history_path": None
        }

# Función helper para ejecutar full tests en un proceso separado
def run_full_test_sync(gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
    """Ejecuta un full test de manera síncrona en un proceso separado."""
    try:
        # Crear el servicio de pruebas
        test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

        # Usar el método sincrónico que maneja su propio bucle de eventos
        result = test_service.run_full_test(
            gherkin_scenario=gherkin_scenario,
            url=url
        )

        return result

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

# Los modelos Pydantic ahora se importan desde src.API.models

# Rutas de API para pruebas
@app.post("/api/tests/smoke", summary="Ejecutar smoke test")
async def run_smoke_test(request: SmokeTestRequest):
    """Ejecuta un smoke test con las instrucciones proporcionadas."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_smoke_test_sync,
                request.instructions,
                request.url,
                request.user_story
            )

        # Verificar que tengamos un resultado válido
        if result is None:
            return JSONResponse(
                status_code=500,
                content={"error": "No se recibió resultado del test"}
            )

        # Transformar la respuesta al formato esperado por el frontend
        transformed_result = transform_backend_response_to_frontend_format(result)

        # Limpiar los datos para serialización JSON
        cleaned_result = clean_data_for_json_serialization(transformed_result)

        # Si el resultado indica fallo pero se ejecutó correctamente (sin 500)
        if isinstance(cleaned_result, dict) and cleaned_result.get('metadata', {}).get('success') is False:
            # Devolver el error pero con código 200 para indicar que la API funcionó
            return JSONResponse(content=cleaned_result)

        # Resultado exitoso
        return JSONResponse(content=cleaned_result)
        
    except Exception as e:
        error_msg = str(e)
        print(f"Error en endpoint smoke test: {error_msg}")
        
        # Solo devolver 500 si realmente hay un error de la API, no del test
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Error interno del servidor: {error_msg}"
            }
        )

@app.post("/api/tests/full", summary="Ejecutar test completo")
async def run_full_test(request: FullTestRequest):
    """Ejecuta un test completo con el escenario Gherkin proporcionado."""
    try:
        # Ejecutar el test en un hilo separado para evitar conflictos de bucle de eventos
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            result = await loop.run_in_executor(
                executor,
                run_full_test_sync,
                request.gherkin_scenario,
                request.url
            )

        # Transformar la respuesta al formato esperado por el frontend
        transformed_result = transform_backend_response_to_frontend_format(result)

        # Limpiar los datos para serialización JSON
        cleaned_result = clean_data_for_json_serialization(transformed_result)
        return JSONResponse(content=cleaned_result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/gherkin", summary="Generar escenario Gherkin")
async def create_gherkin_scenario(
    request: GherkinRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera un escenario Gherkin a partir de instrucciones."""
    try:
        from src.Utilities.response_cleaner import clean_gherkin_response

        # Use the new PromptService for Gherkin generation
        # Create context from the request
        context = {
            "instructions": request.instructions,
            "url": request.url,
            "user_story": request.user_story
        }
        
        # Generate test cases first, then Gherkin scenarios
        if request.user_story:
            # If we have a user story, enhance it and generate manual tests first
            enhanced_story = prompt_service.enhance_user_story(
                user_story=request.user_story,
                language=request.language
            )
            manual_tests = prompt_service.generate_manual_test_cases(
                enhanced_story=enhanced_story,
                language=request.language
            )
            gherkin = prompt_service.generate_gherkin(
                test_cases=manual_tests,
                language=request.language,
                **context
            )
        else:
            # For direct instructions, create a basic test case format
            test_cases = f"Test Case: {request.instructions}"
            gherkin = prompt_service.generate_gherkin(
                test_cases=test_cases,
                language=request.language,
                **context
            )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate/code", summary="Generar cu00f3digo de automatizaciu00f3n")
async def generate_code(
    request: CodeGenerationRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera cu00f3digo de automatizaciu00f3n para un framework especu00edfico."""
    try:
        # Use the new PromptService for code generation
        code = prompt_service.generate_code(
            framework=request.framework,
            gherkin_scenario=request.gherkin_scenario,
            history=request.test_history
        )
        return {"code": code}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Rutas de API para agentes de historias
@app.post("/api/stories/enhance", summary="Mejorar historia de usuario")
async def enhance_story(
    request: EnhanceStoryRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Mejora una historia de usuario."""
    try:
        from src.Utilities.response_cleaner import clean_user_story_response

        # Use the new PromptService for user story enhancement
        enhanced_story = prompt_service.enhance_user_story(
            user_story=request.user_story,
            language=request.language
        )

        # Clean the response
        clean_story = clean_user_story_response(enhanced_story)

        return {"enhanced_story": clean_story}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stories/generate-manual-tests", summary="Generar casos de prueba manuales")
async def generate_manual_tests(
    request: GenerateManualTestsRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera casos de prueba manuales a partir de una historia mejorada."""
    try:
        import json
        import re

        # Use the new PromptService for manual test generation
        manual_tests = prompt_service.generate_manual_test_cases(
            enhanced_story=request.enhanced_story,
            language=request.language
        )

        # First, try to extract JSON if it's wrapped in code blocks
        json_match = re.search(r'```json\s*(.*?)\s*```', manual_tests, re.DOTALL)
        if json_match:
            json_content = json_match.group(1)
            try:
                test_cases_json = json.loads(json_content)
                return {"manual_tests": test_cases_json}
            except json.JSONDecodeError as e:
                # If JSON parsing fails, return the error details for debugging
                return {"manual_tests": json_content, "error": f"JSON parsing error: {str(e)}"}
        
        # If no JSON code block found, try to parse the whole response as JSON
        try:
            test_cases_json = json.loads(manual_tests)
            return {"manual_tests": test_cases_json}
        except json.JSONDecodeError:
            # If all JSON parsing fails, return as string
            return {"manual_tests": manual_tests}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stories/generate-gherkin", summary="Generar escenarios Gherkin desde casos manuales")
async def generate_gherkin_from_manual(
    request: GenerateGherkinRequest,
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Genera escenarios Gherkin a partir de casos de prueba manuales."""
    try:
        from src.Utilities.response_cleaner import clean_gherkin_response

        # Use the new PromptService for Gherkin generation
        gherkin = prompt_service.generate_gherkin(
            test_cases=request.manual_tests,
            language=request.language
        )

        # Clean the response
        clean_gherkin = clean_gherkin_response(gherkin)

        return {"gherkin": clean_gherkin}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/projects/save-history", summary="Guardar historial en proyecto")
async def save_history_to_project(
    request: SaveHistoryRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Guarda un historial de prueba en un proyecto."""
    try:
        test_case = test_service.save_history_to_project(
            project_id=request.project_id,
            suite_id=request.suite_id,
            test_history=request.test_history,
            name=request.name,
            description=request.description,
            gherkin=request.gherkin
        )
        return {"test_case": test_case}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/tests/summarize", summary="Resumir resultados de prueba")
async def summarize_test_results(
    request: SummarizeRequest,
    test_service: TestService = Depends(get_test_service)
):
    """Genera un resumen de los resultados de prueba usando IA."""
    try:
        summary = test_service.summarize_test_results(request.test_results)
        return {"summary": summary}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Endpoint para verificar el estado de la API
@app.get("/api/health", summary="Verificar estado de la API")
async def get_status():
    """Verifica el estado de la API y devuelve informacion basica."""
    return {
        "status": "online",
        "version": "2.0.0",
        "timestamp": datetime.now().isoformat(),
        "api_key_configured": bool(os.environ.get("GOOGLE_API_KEY"))
    }

# Punto de entrada para ejecutar la aplicaciu00f3n
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)